'use client';

import { useState, useEffect, useRef, use } from 'react';

import type { CharacterFormData } from '@/components/CharacterForm';
import { generateSystemMessages, type ChatMessage as ChatMessageType, replaceVariables } from '@/lib/systemMessages';
import { useSession } from 'next-auth/react';
import type { Persona } from '@prisma/client';
import ChatMessage from '@/components/ChatMessage';

type PageParams = {
  params: Promise<{ id: string }>;
};

export default function ChatPage({ params }: PageParams) {
  const chatId = use(params).id;
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [displayMessages, setDisplayMessages] = useState<ChatMessageType[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [character, setCharacter] = useState<CharacterFormData | null>(null);
  const [defaultPersona, setDefaultPersona] = useState<Persona | null>(null);
  const [chatLocale, setChatLocale] = useState<string | null>(null);
  const [streamingMessage, setStreamingMessage] = useState<ChatMessageType | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const streamingRef = useRef<boolean>(false);
  const { data: session } = useSession();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [displayMessages]);

  // Handle page visibility changes and focus events to preserve streaming state
  useEffect(() => {
    const handleVisibilityChange = () => {
      // When page becomes visible again, ensure streaming message is still displayed
      if (!document.hidden && streamingMessage && streamingRef.current) {
        // Force a re-render to show the streaming message
        setDisplayMessages(prev => {
          const withoutStreaming = prev.filter(m => m !== streamingMessage);
          return [...withoutStreaming, streamingMessage];
        });
      }
    };

    const handleFocus = () => {
      // When window regains focus, ensure streaming message is still displayed
      if (streamingMessage && streamingRef.current) {
        setDisplayMessages(prev => {
          const withoutStreaming = prev.filter(m => m !== streamingMessage);
          return [...withoutStreaming, streamingMessage];
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [streamingMessage]);

  // Cleanup streaming state on component unmount
  useEffect(() => {
    return () => {
      streamingRef.current = false;
      setStreamingMessage(null);
    };
  }, []);

  // Load default persona
  useEffect(() => {
    const loadDefaultPersona = async () => {
      if (session?.user) {
        try {
          const response = await fetch('/api/personas');
          if (!response.ok) {
            throw new Error('Failed to load personas');
          }
          const personas = await response.json();
          const defaultPersona = personas.find((p: Persona & { isDefault: boolean }) => p.isDefault);
          setDefaultPersona(defaultPersona || null);
        } catch (error) {
          console.error('Error loading default persona:', error);
        }
      }
    };
    loadDefaultPersona();
  }, [session]);

  // Load chat history and character data
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('Loading chat data...');
        // Load chat history
        const chatResponse = await fetch(`/api/chats/${chatId}`);
        if (!chatResponse.ok) throw new Error('Failed to load chat history');
        const chatData = await chatResponse.json();
        console.log('Chat data loaded:', chatData);
        setMessages(chatData.messages || []);

        if (!chatData.characterId) {
          console.error('No character ID found in chat data');
          return;
        }

        // Load character data
        const characterResponse = await fetch(`/api/characters/${chatData.characterId}`);
        if (!characterResponse.ok) {
          console.error('Failed to load character data:', characterResponse.statusText);
          return;
        }
        const characterData = await characterResponse.json();
        console.log('Character data loaded:', characterData);
        setCharacter(characterData);

        // Load user settings
        if (session?.user) {
          try {
            const settingsResponse = await fetch('/api/user/settings');
            if (settingsResponse.ok) {
              const settings = await settingsResponse.json();
              setChatLocale(settings.chatLocale);
            }
          } catch (error) {
            console.error('Error loading user settings:', error);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, [chatId, session]);

  // Update display messages whenever messages or character changes
  useEffect(() => {
    if (character) {
      const systemMessages = generateSystemMessages(character, session?.user?.name || 'User', defaultPersona);
      const temporaryMessages = systemMessages.filter(m => m.temporary);

      let baseMessages = messages;

      // If there are no user messages yet, show temporary messages
      if (!messages.some(m => m.role === 'user')) {
        baseMessages = [...messages, ...temporaryMessages];
      }

      // Add streaming message if it exists
      if (streamingMessage && streamingRef.current) {
        setDisplayMessages([...baseMessages, streamingMessage]);
      } else {
        setDisplayMessages(baseMessages);
      }
    }
  }, [messages, character, session?.user?.name, defaultPersona, streamingMessage]);

  const saveMessage = async (message: ChatMessageType) => {
    if (message.temporary) {
      console.log('Skipping save for temporary message');
      return;
    }

    try {
      console.log('Saving message:', message);
      const response = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });
      if (!response.ok) {
        throw new Error('Failed to save message');
      }
      console.log('Message saved successfully');
    } catch (error) {
      console.error('Error saving message:', error);
    }
  };

  const sendMessage = async () => {
    console.log('Sending message...');
    console.log('Input:', input);
    console.log('Is loading:', isLoading);
    console.log('Character:', character);

    if (!input.trim() || isLoading || !character) {
      console.log('Send message conditions not met:', {
        inputEmpty: !input.trim(),
        isLoading,
        noCharacter: !character
      });
      return;
    }

    const userMessage: ChatMessageType = {
      role: 'user',
      content: input,
      createdAt: new Date(),
    };

    // Clear input and set loading state first
    setInput('');
    setIsLoading(true);

    try {
      // If this is the first user message, save the greeting message first
      if (!messages.some(m => m.role === 'user')) {
        if (character.firstMessage) {
          const greetingMessage: ChatMessageType = {
            role: 'assistant',
            content: replaceVariables(character.firstMessage, session?.user?.name || 'User', character.name, defaultPersona),
            createdAt: new Date(),
          };
          await saveMessage(greetingMessage);
          setMessages(prev => [...prev, greetingMessage]);
        }
      }

      // Save user message
      console.log('Saving user message:', userMessage);
      await saveMessage(userMessage);

      // Update messages state with user message
      setMessages(prev => [...prev, userMessage]);
      console.log('Updated messages with user message');

      // Always include system messages
      const systemMessages = generateSystemMessages(character, session?.user?.name || 'User', defaultPersona);
      const allMessages = [...systemMessages, ...messages, userMessage];

      // Prepare messages to send to LLM
      const messagesToSend = [...allMessages];

      // Add character notes once before the last N messages if available
      if (character.charNotes && character.charNotesDepth && character.charNotesDepth > 0) {
        const userAndAssistantMessages = messagesToSend.filter(m => m.role === 'user' || m.role === 'assistant');
        const lastNMessages = userAndAssistantMessages.slice(-character.charNotesDepth);

        if (lastNMessages.length > 0) {
          // Insert character notes once before the last N messages
          const firstMsgIndex = messagesToSend.indexOf(lastNMessages[0]);
          if (firstMsgIndex !== -1) {
            messagesToSend.splice(firstMsgIndex, 0, {
              role: 'system',
              content: replaceVariables(character.charNotes!, session?.user?.name || 'User', character.name, defaultPersona),
              temporary: true
            });
          }
        }
      }

      // Add postInstruction as the last system message if available
      if (character.postInstruction) {
        messagesToSend.push({
          role: 'system',
          content: replaceVariables(character.postInstruction, session?.user?.name || 'User', character.name, defaultPersona),
        });
      }

      // Add language instruction if chatLocale is set
      if (chatLocale) {
        const languageMap: { [key: string]: string } = {
          'en': 'English',
          'zh-CN': '简体中文 (Simplified Chinese)',
          'zh-TW': '繁體中文 (Traditional Chinese)'
        };

        messagesToSend.push({
          role: 'system',
          content: `Please respond in ${languageMap[chatLocale] || chatLocale}. All your responses should be in ${languageMap[chatLocale] || chatLocale} unless explicitly requested otherwise.`
        });
      }

      console.log('All messages to send:', messagesToSend);

      console.log('Sending request to LLM API...');
      const response = await fetch('http://192.168.86.41:5001/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: messagesToSend.map(m => ({
            role: m.role,
            content: m.content,
            name: m.name ? m.name : undefined
          })),
          model: 'koboldcpp/Evathene-v1.3-Q4_K_S',
          temperature: 1.02,
          max_tokens: 500,
          stream: true,
          presence_penalty: 0.05,
          frequency_penalty: 0.04,
          top_p: 1
        }),
      });

      console.log('LLM API response received:', response);
      const reader = response.body?.getReader();
      const assistantMessage: ChatMessageType = {
        role: 'assistant',
        content: '',
        createdAt: new Date(),
      };

      if (reader) {
        console.log('Starting to read stream...');
        let buffer = '';

        // Set streaming state
        streamingRef.current = true;
        setStreamingMessage(assistantMessage);

        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log('Stream reading complete');
            break;
          }

          // Decode the chunk and add to buffer
          buffer += new TextDecoder().decode(value);

          // Process complete SSE messages
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep the last incomplete line in the buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6)); // Remove 'data: ' prefix
                if (data.choices && data.choices[0]?.delta?.content) {
                  assistantMessage.content += data.choices[0].delta.content;
                  console.log('Updated assistant message:', assistantMessage.content);

                  // Update the streaming message state
                  setStreamingMessage({ ...assistantMessage });
                }
              } catch (error) {
                console.log('Error parsing SSE JSON:', error);
              }
            }
          }
        }

        // Clear streaming state and add final message to messages
        streamingRef.current = false;
        setStreamingMessage(null);
        setMessages(prev => [...prev, assistantMessage]);

        // Save assistant message after it's complete
        console.log('Saving final assistant message');
        await saveMessage(assistantMessage);
      }
    } catch (error) {
      console.error('Error in message processing:', error);

      // Clean up streaming state on error
      streamingRef.current = false;
      setStreamingMessage(null);

      const errorMessage: ChatMessageType = {
        role: 'assistant',
        content: 'Sorry, something went wrong.',
        createdAt: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
      await saveMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-64px)] max-w-2xl mx-auto">
      <div className="bg-gray-900 p-4 border-b border-gray-800">
        <h1 className="text-xl font-bold text-gray-100">
          {character ? `Chat with ${character.name}` : 'Chat'}
        </h1>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-900">
        {displayMessages.filter(m => m.role !== 'system').map((message, index) => (
          <ChatMessage key={message.id || index} message={message} />
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-800 px-4 py-2 rounded-xl max-w-[80%] shadow-sm text-gray-100">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="bg-gray-900 p-4 border-t">
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-1 px-4 py-2 border border-gray-800 rounded-full focus:ring-2 focus:ring-primary text-gray-100 bg-gray-800"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
            disabled={isLoading}
          />
          <button
            className="bg-gray-800 text-gray-100 px-4 py-2 rounded-full hover:bg-gray-700 transition-colors disabled:opacity-50"
            onClick={sendMessage}
            disabled={isLoading}
          >
            {isLoading ? 'Sending...' : 'Send'}
          </button>
        </div>
      </div>
    </div>
  );
}
