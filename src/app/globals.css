/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'); */

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 17 24 39;
    --card: 255 255 255;
    --card-foreground: 17 24 39;
    --popover: 255 255 255;
    --popover-foreground: 17 24 39;
    --primary: 79 70 229;
    --primary-foreground: 255 255 255;
    --secondary: 236 72 153;
    --secondary-foreground: 255 255 255;
    --muted: 243 244 246;
    --muted-foreground: 107 114 128;
    --accent: 147 51 234;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 229 231 235;
    --input: 229 231 235;
    --ring: 79 70 229;
    --radius: 0.5rem;
  }

  .dark {
    --background: 17 24 39;
    --foreground: 255 255 255;
    --card: 17 24 39;
    --card-foreground: 255 255 255;
    --popover: 17 24 39;
    --popover-foreground: 255 255 255;
    --primary: 79 70 229;
    --primary-foreground: 255 255 255;
    --secondary: 236 72 153;
    --secondary-foreground: 255 255 255;
    --muted: 31 41 55;
    --muted-foreground: 156 163 175;
    --accent: 147 51 234;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 31 41 55;
    --input: 31 41 55;
    --ring: 79 70 229;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  h1 {
    @apply text-4xl font-bold text-gradient-primary;
  }
  
  h2 {
    @apply text-3xl font-bold text-gradient-secondary;
  }
  
  h3 {
    @apply text-2xl font-semibold;
  }
  
  .glass {
    @apply bg-opacity-20 backdrop-blur-lg border border-opacity-20;
  }
  
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }
  
  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent;
  }
}
