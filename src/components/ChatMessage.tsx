import type { ChatMessage as ChatMessageType } from '@/lib/systemMessages';
import ReactMarkdown from 'react-markdown';

interface ChatMessageProps {
    message: ChatMessageType;
}

const ChatMessage = ({ message }: ChatMessageProps) => {
    return (
        <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} w-full`}>
            <div className={`px-4 py-2 rounded-xl max-w-[80%] w-fit ${message.role === 'user'
                ? 'bg-blue-800 text-white'
                : 'bg-gray-800 text-white'
                }`}>
                <div className={`prose prose-sm max-w-none break-words overflow-hidden whitespace-pre-line ${message.role === 'user'
                    ? 'prose-p:text-white prose-em:text-gray-400 prose-strong:text-orange-400 prose-h3:text-white prose-code:text-gray-300 prose-em:text-gray-400 prose-quotations:text-orange-400'
                    : 'prose-p:text-white prose-em:text-gray-400 prose-strong:text-orange-400 prose-h3:text-white prose-code:text-gray-300 prose-em:text-gray-400 prose-quotations:text-orange-400'
                    } prose-p:my-0 prose-p:leading-normal prose-pre:whitespace-pre-wrap prose-pre:break-words prose-code:break-words prose-h3:text-lg prose-h3:font-bold prose-h3:my-2`}>
                    <ReactMarkdown components={{
                        p: ({ children }) => <p style={{ maxWidth: '100%', overflowWrap: 'break-word' }} className="whitespace-pre-line">{children}</p>,
                        code: ({ children }) => <code style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', backgroundColor: 'transparent' }}>{children}</code>,
                        pre: ({ children }) => <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{children}</pre>,
                        em: ({ children }) => <em className="font-normal">{children}</em>,
                        h3: ({ children }) => <h3 className="text-lg font-bold my-2">{children}</h3>
                    }}>{message.content.replace(/[""]([^""]+)[""]/g, '**"$1"**')}</ReactMarkdown>
                </div>
                {message.createdAt && !message.temporary && (
                    <p className="text-xs mt-1 opacity-70">
                        {new Date(message.createdAt).toLocaleTimeString()}
                    </p>
                )}
            </div>
        </div>
    );
};

export default ChatMessage; 
